import CloseRoundedIcon from "@mui/icons-material/CloseRounded";
import { useDispatch, useSelector } from "react-redux";
import {
  setCallTranscriptOpen,
  setIsTranscriptVisible,
} from "@/redux/BusinessDashboard/subSlices/RootSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { useTranslation } from "react-i18next";
import { cn } from "@/lib/utils";
import React, { useEffect } from "react";
import CircularLoaderSmall from "@/components/Loaders/CircularLoaderSmall";
import { useTranslationButton } from "@/hooks/useTranslationButton";

function TranscriptLine({
  line,
  isAI,
  sender,
  senderColor,
}: {
  line: any;
  isAI: boolean;
  sender: string;
  senderColor: string;
}) {
  const { translatedText, showTranslation, loading, button } =
    useTranslationButton({
      text: line.text,
    });

  const buttonClassName = showTranslation
    ? "bg-voxa-neutral-800"
    : "text-voxa-neutral-800 border-voxa-neutral-800";

  const buttonWithClass = React.cloneElement(button, {
    className: cn(button.props.className, buttonClassName, "w-6 h-6"),
  });

  return (
    <div className={`flex w-full ${isAI ? "justify-start" : "justify-end"}`}>
      <div className={`rounded-xl p-2 max-w-[75%] ${senderColor}`}>
        <div className="flex items-center justify-between gap-2">
          <div className="text-xs font-medium mb-1">{sender}</div>
          <div>{buttonWithClass}</div>
        </div>
        <div className="text-xs font-light text-voxa-neutral-800 dark:text-voxa-neutral-50 whitespace-pre-line">
          <span>
            {loading ? (
              <span className="flex justify-center items-center w-full">
                <CircularLoaderSmall className="scale-75" />
              </span>
            ) : (
              translatedText
            )}
          </span>
        </div>
      </div>
    </div>
  );
}

export default function Transcript() {
  const { t } = useTranslation("callLogs");
  const dispatch = useDispatch<AppDispatch>();
  const { callTranscriptOpen, isTranscriptVisible, NotesOpen, callDetails } =
    useSelector(
      (state: RootState) => state.businessDashboard.businessDashboardRoot
    );
  useEffect(() => {
    let timeout: NodeJS.Timeout;
    if (callTranscriptOpen) {
      timeout = setTimeout(() => dispatch(setIsTranscriptVisible(true)), 0.5);
    } else {
      dispatch(setIsTranscriptVisible(false));
    }
    return () => clearTimeout(timeout);
  }, [callTranscriptOpen, dispatch]);

  const senderColors = [
    "bg-green-300/60 dark:bg-green-300/60",
    "bg-yellow-100 dark:bg-yellow-300/60",
    "bg-purple-100 dark:bg-purple-300/60",
    "bg-orange-100 dark:bg-orange-300/60",
    "bg-red-100 dark:bg-red-300/60",
    "bg-teal-100 dark:bg-teal-300/60",
    "bg-gray-100 dark:bg-gray-300/60",
    "bg-amber-100 dark:bg-amber-300/60",
    "bg-lime-100 dark:bg-lime-300/60",
    "bg-cyan-100 dark:bg-cyan-300/60",
  ];
  const senderColorMap: Record<string, string> = {};
  let colorIdx = 0;

  function getSenderColor(sender: string) {
    if (senderColorMap[sender]) return senderColorMap[sender];
    if (sender === "🤖 IA" || sender.includes("IA"))
      return "bg-blue-100 dark:bg-blue-400/80 text-black dark:text-white";
    const color =
      senderColors[colorIdx % senderColors.length] +
      " text-black dark:text-white";
    senderColorMap[sender] = color;
    colorIdx++;
    return color;
  }

  return (
    <div
      className={cn(
        "relative max-md:fixed max-w-md w-full h-full top-0 right-0 duration-300 transition-all ease-in-out -ml-[450px]",
        isTranscriptVisible && "-ml-0",
        NotesOpen && "duration-500"
      )}
    >
      <div
        className={cn(
          "z-50 rounded-sm max-w-md p-3 w-full flex flex-col gap-4 h-screen items-center relative  md:fixed right-0 rtl:right-auto rtl:left-0 top-0 bg-voxa-neutral-50/80 dark:bg-black/60 backdrop-blur-sm duration-300 transition-all ease-in-out translate-x-[450px]",
          isTranscriptVisible && "translate-x-0",
          NotesOpen && "duration-500"
        )}
      >
        <div className="flex justify-between items-center w-full text-foreground/50 dark:text-voxa-neutral-50">
          <h1 className="ml-6 w-full text-center text-xl font-semibold">
            {t("callDetails.title")}
          </h1>
          <button
            onClick={() => {
              dispatch(setIsTranscriptVisible(false));
              setTimeout(() => {
                dispatch(setCallTranscriptOpen(false));
              }, 200);
            }}
            className="hover:text-gray-400 transition-all duration-150"
          >
            <CloseRoundedIcon />
          </button>
        </div>
        <div className="flex flex-col flex-1 w-full gap-0 justify-between h-full min-h-0">
          {Array.isArray(callDetails.transcript) && (
            <div className="relative rounded-lg bg-voxa-neutral-100/40 dark:bg-sidebar overflow-y-auto  flex flex-col gap-2 w-full flex-1 p-3 min-h-0">
              {callDetails.transcript.map((line: any, idx: number) => {
                const isAI = line.from?.includes("IA");
                const sender = isAI ? "🤖 IA" : line.from;
                const senderColor = getSenderColor(sender);
                return (
                  <TranscriptLine
                    key={line.id || idx}
                    line={line}
                    isAI={isAI}
                    sender={sender}
                    senderColor={senderColor}
                  />
                );
              })}
              {/* Color legend */}
              <div className="mt-5 h-full flex flex-wrap gap-2 w-full justify-center items-end bg-transparent">
                {Object.entries(senderColorMap)
                  .filter(
                    ([sender]) =>
                      sender !== "🤖 IA" && sender && sender.trim() !== ""
                  )
                  .map(([sender, color], idx) => (
                    <div key={idx} className={`flex items-center gap-1`}>
                      <div className={`w-5 h-5 rounded ${color}`} />
                      <span className="text-xs text-voxa-neutral-800 dark:text-voxa-neutral-100">
                        {sender}
                      </span>
                    </div>
                  ))}
                {/* Always show IA color */}
                <div className="flex items-center gap-1">
                  <div className="w-5 h-5 rounded bg-blue-100 dark:bg-blue-400/90" />
                  <span className="text-xs text-voxa-neutral-800 dark:text-voxa-neutral-100">
                    🤖 IA
                  </span>
                </div>
              </div>
            </div>
          )}
          <div className="py-2">
            {/* Entities section */}
            {Array.isArray(callDetails?.batch_transcript_entities) &&
              callDetails.batch_transcript_entities.length > 0 && (
                <div>
                  <p className="pb-3 text-lg font-semibold text-center">
                    {t("entities")}
                  </p>
                  <div className="grid grid-cols-[4fr_4fr_6fr] gap-x-3 rounded-lg max-h-[25vh] overflow-y-auto px-3 pb-1 bg-voxa-neutral-100/40 dark:bg-sidebar">
                    <p className="text-center font-semibold text-md text-voxa-teal-600 py-1.5">
                      {t("speaker")}
                    </p>
                    <p className="text-center font-semibold text-md text-voxa-teal-600 py-1.5">
                      {t("type")}
                    </p>
                    <p className="text-center font-semibold text-md text-voxa-teal-600 py-1.5">
                      {t("text")}
                    </p>
                    {callDetails.batch_transcript_entities.map(
                      (entity: any, idx: number) => (
                        <React.Fragment key={idx}>
                          <div className="py-2 text-xs font-semibold text-voxa-neutral-700 dark:text-voxa-neutral-100 border-t border-voxa-neutral-300 dark:border-voxa-neutral-700">
                            {entity.speaker}
                          </div>
                          <div className="py-2 text-xs font-semibold text-voxa-neutral-700 dark:text-voxa-neutral-100 border-t border-voxa-neutral-300 dark:border-voxa-neutral-700">
                            {entity.entity_type}
                          </div>
                          <div className="py-2 text-xs text-voxa-neutral-800 dark:text-voxa-neutral-100 border-t border-voxa-neutral-300 dark:border-voxa-neutral-700">
                            {entity.text}
                          </div>
                        </React.Fragment>
                      )
                    )}
                  </div>
                </div>
              )}
          </div>
        </div>
      </div>
    </div>
  );
}
