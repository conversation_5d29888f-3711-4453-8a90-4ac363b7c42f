import { <PERSON><PERSON><PERSON>en, <PERSON>I<PERSON>, XIcon, CheckIcon } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import {
  Drawer,
  DrawerContent,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import {
  handleOpenCallDetails,
  removeTagFromCallDetails,
} from "@/redux/BusinessDashboard/subSlices/RootSlice";
import { Input } from "@/components/ui/input";
import { useEffect, useState } from "react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  UpdateConversationNotes,
  getConversationNotesAndTagsById,
} from "@/actions/ConversationActions";
import { addTagToCallDetails } from "@/redux/BusinessDashboard/subSlices/RootSlice";
import { useSelector, useDispatch } from "react-redux";
import { RootState, AppDispatch } from "@/redux/store";

export default function TagsNotesDrawer() {
  const { callDetails, NotesOpen } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardRoot
  );
  const dispatch = useDispatch<AppDispatch>();

  const [drawerOpen, setDrawerOpen] = useState(false);
  const [localNotes, setLocalNotes] = useState(callDetails?.notes || "");
  const [localTags, setLocalTags] = useState(callDetails?.tags || []);
  const [addingTag, setAddingTag] = useState(false);
  const [tagName, setTagName] = useState("");
  const [tagColor, setTagColor] = useState("#10B981");
  const tagColors = [
    "#10B981",
    "#3B82F6",
    "#F59E0B",
    "#EF4444",
    "#8B5CF6",
    "#06B6D4",
    "#84CC16",
    "#F97316",
    "#EC4899",
    "#6B7280",
  ];

  useEffect(() => {
    async function fetchNotesAndTags() {
      if (NotesOpen && callDetails?._id) {
        const res = await getConversationNotesAndTagsById(callDetails._id);
        if (res.success) {
          setLocalNotes(res.notes || "");
          setLocalTags(res.tags || []);
        }
      }
    }
    fetchNotesAndTags();
  }, [NotesOpen, callDetails?._id, callDetails?.tags]);

  const handleUpdateNotes = async () => {
    if (localNotes !== callDetails?.notes && callDetails?._id) {
      await UpdateConversationNotes(callDetails._id, localNotes);
      dispatch(handleOpenCallDetails({ ...callDetails, notes: localNotes }));
    }
  };

  const handleDrawerOpenChange = async (open: boolean) => {
    setDrawerOpen(open);
    if (!open) {
      handleUpdateNotes();
    }
  };

  const handleAddTag = async () => {
    if (!tagName.trim() || !callDetails?._id) return;
    await dispatch(
      addTagToCallDetails({
        conversationId: callDetails._id,
        tagName: tagName.trim(),
        tagColor,
      }) as any
    );
    setTagName("");
    setTagColor("#10B981");
    setAddingTag(false);
  };

  return (
    <Drawer open={drawerOpen} onOpenChange={handleDrawerOpenChange}>
      <DrawerTrigger asChild>
        <div className="size-8 p-0">
          <Tooltip>
            <TooltipTrigger asChild className="absolute">
              <Button
                variant="outlined"
                className="size-8 rounded-full bg-background"
              >
                <NotebookPen className="size-7 dark:text-voxa-neutral-200 cursor-pointer" />
                {(callDetails?.tags.length > 0 || callDetails?.notes) && (
                  <div className="absolute top-1.5 right-[3px] size-[5px] rounded-full bg-red-500" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent className="bg-white fill-white dark:fill-sidebar dark:bg-sidebar dark:border-sidebar-border">
              Tags and Notes
            </TooltipContent>
          </Tooltip>
        </div>
      </DrawerTrigger>
      <DrawerContent className="flex flex-col justify-between max-w-md p-5">
        {/* Tags */}
        <div className="flex flex-col gap-3">
          <DrawerTitle className="-mt-2">Tags</DrawerTitle>
          <div className="flex justify-center flex-wrap gap-2 mb-2">
            {localTags.map((tag: any, idx: number) => (
              <span
                key={idx}
                className="flex items-center gap-0.5 pl-2 pr-1 py-1 rounded-full text-xs font-medium border"
                style={{
                  background: tag.color + "22",
                  color: tag.color,
                  borderColor: tag.color + "55",
                }}
              >
                <span
                  className="w-2 h-2 rounded-full mr-1"
                  style={{ background: tag.color }}
                />
                {tag.name}
                <button
                  onClick={async () => {
                    if (!callDetails?._id) return;
                    await dispatch(
                      removeTagFromCallDetails({
                        conversationId: callDetails._id,
                        tagName: tag.name,
                        tagColor: tag.color,
                      }) as any
                    );
                  }}
                  title="Delete Tag"
                >
                  <XIcon style={{ color: tag.color }} className="w-3.5 h-3.5" />
                </button>
              </span>
            ))}
            {!addingTag && (
              <button
                className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-voxa-neutral-800 transition"
                onClick={() => setAddingTag(true)}
                title="Add Tag"
              >
                <PlusIcon className="w-5 h-5 text-voxa-teal-600" />
              </button>
            )}
          </div>
          {addingTag && (
            <>
              <div className="w-full flex justify-center items-center gap-1.5">
                <Input
                  value={tagName}
                  onChange={(e) => setTagName(e.target.value)}
                  placeholder="Tag name"
                  className="h-[30px] placeholder:text-xs text-xs file:text-xs rounded-full px-2.5 py-1"
                  autoFocus
                />
                <button
                  className="p-1 rounded-full hover:bg-voxa-teal-100 dark:hover:bg-voxa-teal-200"
                  onClick={handleAddTag}
                  title="Confirm"
                >
                  <CheckIcon className="w-4 h-4 text-voxa-teal-600" />
                </button>
                <button
                  className="p-1 rounded-full hover:bg-red-100 dark:hover:bg-red-200"
                  onClick={() => {
                    setAddingTag(false);
                    setTagName("");
                    setTagColor("#10B981");
                  }}
                  title="Cancel"
                >
                  <XIcon className="w-4 h-4 text-red-500" />
                </button>
              </div>
              <div className="flex justify-center gap-1">
                {tagColors.map((color) => (
                  <button
                    key={color}
                    type="button"
                    className={`z-50 w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                      tagColor === color
                        ? "border-voxa-teal-600"
                        : "border-gray-300"
                    }`}
                    style={{ background: color }}
                    onClick={() => setTagColor(color)}
                    aria-label={`Choose color ${color}`}
                  >
                    {tagColor === color && (
                      <CheckIcon className="w-3 h-3 text-white" />
                    )}
                  </button>
                ))}
              </div>
            </>
          )}
        </div>
        {/* Notes */}
        <div className="flex flex-col gap-3">
          <DrawerTitle className="-mt-2">Notes</DrawerTitle>
          <Textarea
            placeholder="Write some notes for this call"
            className="h-60 bg-voxa-neutral-50 dark:bg-voxa-neutral-950 border border-voxa-neutral-100 dark:border-voxa-neutral-700/80 min-h-24 rounded-lg"
            value={localNotes}
            onChange={(e) => setLocalNotes(e.target.value)}
            onBlur={handleUpdateNotes}
          />
          <p className="text-center text-sm">Notes are auto-saved</p>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
