"use client";

import { useTranslation } from "react-i18next";
import {
  AudioLinesIcon,
  Blocks,
  BotIcon,
  HandCoins,
  ScanIcon,
  TextSelectIcon,
  WorkflowIcon,
  UserRoundCog,
  UsersRound,
  ChartArea,
  ExternalLink,
  Cog,
  WebhookIcon,
  CodeIcon,
  Puzzle,
  Archive,
  SmilePlus,
  ShieldCheck,
} from "lucide-react";
import LogoutIcon from "@mui/icons-material/Logout";
import { signOut, useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import parrot from "@/public/images/Icons/parrot.svg";
import SettingsDropdown from "../Nav/SettingsDropdown";
import { Button } from "../ui/button";
import clsx from "clsx";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarHeader,
  SidebarMenu,
  SidebarTrigger,
  useSidebar,
} from "../ui/sidebar";
import React from "react";
import DashSidebarItem, { SidebarItemProps } from "./SidebarItem";
import DashSidebarGroupItem, {
  SidebarGroupItemProps,
} from "./SidebarGroupItem";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";
import { EndCurrentConnection } from "@/actions/ConnectionActions";

export default function BusinessAdminDashSidebar() {
  const { t } = useTranslation();
  const { open } = useSidebar();
  const { data: session } = useSession();
  const [isRTL, setIsRTL] = React.useState(false);

  React.useEffect(() => {
    setIsRTL(document.dir === "rtl");
    const observer = new MutationObserver(() => {
      setIsRTL(document.dir === "rtl");
    });
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ["dir"],
    });
    return () => observer.disconnect();
  }, []);

  const Signout = async () => {
    await EndCurrentConnection();
    signOut({ redirect: false });
    window.location.href = "/api/auth/cognito-logout";
  };

  const data: {
    items: SidebarItemProps[];
    groupItems: SidebarGroupItemProps[];
  } = {
    items: [
      {
        icon: ExternalLink,
        title: t("sidebar.overview"),
        url: "/businessDash",
        alert: false,
      },
      {
        icon: BotIcon,
        title: t("sidebar.assistants"),
        url: "/businessDash/assistants",
        alert: false,
      },
      {
        icon: TextSelectIcon,
        title: t("sidebar.scripts"),
        url: "/businessDash/scripts",
        alert: false,
      },
      {
        icon: UsersRound,
        title: t("sidebar.clients"),
        url: "/businessDash/clients",
        alert: false,
      },
      {
        icon: ScanIcon,
        title: t("sidebar.rag"),
        url: "/businessDash/rag",
        alert: true,
      },
      {
        icon: AudioLinesIcon,
        title: t("sidebar.voices"),
        url: "/businessDash/voices",
        alert: true,
      },
      {
        icon: ChartArea,
        title: t("sidebar.analytics"),
        url: "/businessDash/analytics",
        alert: true,
      },
    ],
    groupItems: [
      {
        icon: Blocks,
        title: t("sidebar.integrations"),
        items: [
          {
            icon: WorkflowIcon,
            title: t("sidebar.integrations"),
            url: "/businessDash/integrations",
            alert: false,
          },
          {
            icon: Archive,
            title: t("sidebar.storage"),
            url: "/businessDash/integrations/storage",
            alert: false,
          },
          {
            icon: Puzzle,
            title: t("sidebar.plugins"),
            url: "/businessDash/integrations/plugins",
            alert: false,
          },
        ],
      },
      {
        icon: Cog,
        title: "Account Settings",
        items: [
          {
            icon: UserRoundCog,
            title: t("sidebar.account"),
            url: "/businessDash/settings",
            alert: false,
          },
          {
            icon: SmilePlus,
            title: t("sidebar.agents"),
            url: "/businessDash/settings/agents",
            alert: false,
          },
          {
            icon: HandCoins,
            title: t("sidebar.billing"),
            url: "/businessDash/settings/billing",
            alert: false,
          },
          {
            icon: ShieldCheck,
            title: t("sidebar.security"),
            url: "/businessDash/settings/security",
            alert: false,
          },
        ],
      },
      {
        icon: CodeIcon,
        title: "Developer",
        items: [
          {
            icon: WebhookIcon,
            title: t("sidebar.webhooks"),
            url: "/businessDash/webhooks",
            alert: false,
          },
        ],
      },
    ],
  };

  return (
    <Sidebar
      side={isRTL ? "right" : "left"}
      collapsible="icon"
      className="fixed top-0 bottom-0 h-[100dvh]"
    >
      <SidebarHeader className={clsx("p-2 flex-shrink-0", !open && "h-[84px]")}>
        <div
          className={clsx(
            "w-full flex items-center",
            open ? "justify-between" : "flex-col"
          )}
        >
          <Link href="/" className="flex items-center">
            <Image
              src={parrot}
              alt="Echo Parrot Icon"
              width={10}
              className={clsx(
                "m-1.5 w-[24px] invert dark:invert-0",
                !open &&
                  "scale-125 delay-200 duration-200 transition-transform pb-1"
              )}
            />
            <h1
              className={clsx(
                "whitespace-nowrap overflow-hidden transition-all duration-150 text-xl font-semibold",
                open ? "w-[115px]" : "w-0"
              )}
            >
              {t("logo")}
            </h1>
          </Link>
          <SidebarTrigger />
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup className={clsx(!open && "px-1.5")}>
          <SidebarMenu>
            {data.items.slice(0, 6).map((item, index) => (
              <DashSidebarItem key={index} item={item} />
            ))}
            {data.groupItems.map((item, index) => (
              <DashSidebarGroupItem key={index} item={item} />
            ))}
            {data.items.slice(6, 7).map((item, index) => (
              <DashSidebarItem key={index} item={item} />
            ))}
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter
        className={clsx(
          "w-full border-t border-input p-2",
          !open && "h-24 pt-0.5"
        )}
      >
        <div
          className={clsx(
            "w-full flex items-center gap-x-1",
            open ? "max-sm:max-w-full" : "flex-col gap-0.5 mt-0.5"
          )}
        >
          <Button
            onClick={Signout}
            variant="outline"
            className="p-0 min-w-10 min-h-10 rounded-lg text-voxa-neutral-900 dark:text-voxa-neutral-500 hover:bg-voxa-neutral-200 dark:hover:bg-voxa-neutral-900 transition-all duration-300"
          >
            <LogoutIcon className="text-red-600 cursor-pointer transition-colors duration-150 scale-115" />
          </Button>
          <div
            className={clsx(
              "px-1 flex flex-col justify-center items-center transition-all whitespace-nowrap overflow-hidden duration-150 ease-in-out",
              !open && "w-0 h-0"
            )}
          >
            <Tooltip>
              <TooltipTrigger className="text-left w-full font-semibold truncate leading-5">
                {session?.user.name}
              </TooltipTrigger>
              <TooltipContent>{session?.user.name}</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger className="text-left w-full text-xs text-gray-500 truncate">
                {session?.user.email}
              </TooltipTrigger>
              <TooltipContent>{session?.user.email}</TooltipContent>
            </Tooltip>
          </div>
          <SettingsDropdown
            variant="outline"
            contentClass="mb-1"
            triggerClass={clsx(
              "h-10 w-10 rounded-lg",
              open && "ml-auto rtl:ml-0 rtl-mr-auto"
            )}
          />
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
