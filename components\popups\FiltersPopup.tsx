import SearchRoundedIcon from "@mui/icons-material/SearchRounded";
import { DatePickerWithRange } from "@/components/Inputs/Datepicker";
import { PhoneCall, PhoneIcon, UserIcon } from "lucide-react";
import CustomInput from "../CustomFormItems/Input";
import CustomButton from "../CustomFormItems/Button";
import CircularLoaderSmall from "../Loaders/CircularLoaderSmall";
import {
  setFiltersOpen,
  setConversationID,
  setCallSid,
  setRecepientName,
  setRecepientPhone,
  setDate,
  setCallType,
  setConversationType,
  getConvertsationsByFilters,
  removeFilters,
  removeAllFilters,
} from "@/redux/BusinessDashboard/subSlices/RootSlice";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/redux/store";
import { DateRange } from "react-day-picker";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { useTranslation } from "react-i18next";

export default function FiltersPopup() {
  const { t } = useTranslation("callLogs");
  const dispatch = useDispatch<AppDispatch>();
  const {
    callType,
    date,
    callSid,
    RecepientName,
    RecepientPhone,
    conversationID,
    conversationType,
    loading,
  } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardRoot
  );

  const handleConversationTypeChange = (type: string) => {
    const newConversationType: any = { ...conversationType };
    newConversationType[type] = !newConversationType[type];
    dispatch(setConversationType(newConversationType));
  };

  const handleCallTypeChange = (type: string) => {
    const newCallType: any = { ...callType };
    newCallType[type] = !newCallType[type];
    dispatch(setCallType(newCallType));
  };

  const handleFilterContacts = async () => {
    await dispatch(getConvertsationsByFilters());
  };

  return (
    <Dialog open={true} onOpenChange={(open) => dispatch(setFiltersOpen(open))}>
      <DialogContent className="px-0">
        <DialogHeader>
          <DialogTitle>{t("filters.title")}</DialogTitle>
        </DialogHeader>

        <div className="h-[calc(100vh-12rem)] overflow-y-auto px-4 flex flex-col gap-3">
          <CustomInput
            props={{
              value: callSid,
              name: "callsid",
              placeholder: t("filters.sid"),
              type: "text",
              required: false,
              className: "w-full",
              label: t("filters.sid"),
              labelIcon: <PhoneCall className="w-4 h-4" />,
              onChange: (e: any) => dispatch(setCallSid(e.target.value)),
            }}
          />
          <CustomInput
            props={{
              value: conversationID,
              name: "conversationID",
              placeholder: t("filters.conversationId"),
              type: "text",
              required: false,
              className: "w-full",
              parentClassName: "mt-4",
              label: t("filters.conversationId"),
              labelIcon: <PhoneCall className="w-4 h-4" />,
              onChange: (e: any) => dispatch(setConversationID(e.target.value)),
            }}
          />

          <div className="flex flex-col gap-2 mt-3">
            <p className="text-lg font-semibold text-foreground/50">
              {t("filters.recipientDetails")}
            </p>
            <div className="flex gap-2 flex-col">
              <CustomInput
                props={{
                  value: RecepientName,
                  name: "recepientname",
                  placeholder: t("filters.name"),
                  type: "text",
                  required: false,
                  className: "w-full",
                  parentClassName: "mt-2",
                  label: t("filters.name"),
                  labelIcon: <UserIcon className="w-4 h-4" />,
                  onChange: (e: any) =>
                    dispatch(setRecepientName(e.target.value)),
                }}
              />
              <CustomInput
                props={{
                  value: RecepientPhone,
                  name: "recepientphone",
                  placeholder: t("filters.phoneNumber"),
                  type: "text",
                  required: false,
                  className: "w-full",
                  parentClassName: "mt-2",
                  label: t("filters.phoneNumber"),
                  labelIcon: <PhoneIcon className="w-4 h-4" />,
                  onChange: (e: any) =>
                    dispatch(setRecepientPhone(e.target.value)),
                }}
              />
            </div>
          </div>
          <div className="flex flex-col gap-2 mt-3">
            <p className="text-lg font-semibold text-foreground/50">
              {t("filters.conversationType")}
            </p>
            <div className="grid grid-cols-4 gap-2">
              <CustomButton
                props={{
                  value: t("filters.types.whatsapp"),
                  className: `dark:bg-voxa-neutral-900 px-2 hover:bg-voxa-teal-600/30 dark:hover:bg-voxa-teal-600/30 ${
                    conversationType.whatsapp
                      ? "bg-voxa-teal-600/50 dark:bg-voxa-teal-600/50"
                      : ""
                  }`,
                  onClick: () => handleConversationTypeChange("whatsapp"),
                }}
              />
              <CustomButton
                props={{
                  value: t("filters.types.sms"),
                  className: `dark:bg-voxa-neutral-900 px-2 hover:bg-green-500/30 dark:hover:bg-green-500/30 ${
                    conversationType.sms
                      ? "bg-green-500/50 dark:bg-green-500/50"
                      : ""
                  }`,
                  onClick: () => handleConversationTypeChange("sms"),
                }}
              />
              <CustomButton
                props={{
                  value: t("filters.types.call"),
                  className: `dark:bg-voxa-neutral-900 px-2 hover:bg-purple-500/30 dark:hover:bg-purple-500/30 ${
                    conversationType.call
                      ? "bg-purple-500/50 dark:bg-purple-500/50"
                      : ""
                  }`,
                  onClick: () => handleConversationTypeChange("call"),
                }}
              />
              <CustomButton
                props={{
                  value: t("filters.types.meet"),
                  className: `dark:bg-voxa-neutral-900 px-2 hover:bg-yellow-500/30 dark:hover:bg-yellow-500/30 ${
                    conversationType.meet
                      ? "bg-yellow-500/50 dark:bg-yellow-500/50"
                      : ""
                  }`,
                  onClick: () => handleConversationTypeChange("meet"),
                }}
              />
            </div>
          </div>
          <div className="flex flex-col gap-2 mt-3">
            <p className="text-lg font-semibold text-foreground/50">
              {t("filters.interactionType")}
            </p>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
              <CustomButton
                props={{
                  value: t("filters.types.incoming"),
                  className: `dark:bg-voxa-neutral-900 px-0 hover:bg-blue-500/30 dark:hover:bg-blue-500/30 ${
                    callType.incoming
                      ? "bg-blue-500/50 dark:bg-blue-500/50"
                      : ""
                  }`,
                  onClick: () => handleCallTypeChange("incoming"),
                }}
              />
              <CustomButton
                props={{
                  value: t("filters.types.outgoing"),
                  className: `dark:bg-voxa-neutral-900 px-0 hover:bg-orange-500/30 dark:hover:bg-orange-500/30 ${
                    callType.outgoing
                      ? "bg-orange-500/50 dark:bg-orange-500/50"
                      : ""
                  }`,
                  onClick: () => handleCallTypeChange("outgoing"),
                }}
              />
              <CustomButton
                props={{
                  value: t("filters.types.missed"),
                  className: `dark:bg-voxa-neutral-900 px-0 hover:bg-red-500/30 dark:hover:bg-red-500/30 ${
                    callType.missed ? "bg-red-500/50 dark:bg-red-500/50" : ""
                  }`,
                  onClick: () => handleCallTypeChange("missed"),
                }}
              />
              <CustomButton
                props={{
                  value: t("filters.types.answered"),
                  className: `dark:bg-voxa-neutral-900 px-0 hover:bg-green-500/30 dark:hover:bg-green-500/30 ${
                    callType.answered
                      ? "bg-green-500/50 dark:bg-green-500/50"
                      : ""
                  }`,
                  onClick: () => handleCallTypeChange("answered"),
                }}
              />
            </div>
          </div>

          <div className="flex flex-col gap-2 mt-3">
            <p className="text-lg font-semibold text-foreground/50">
              {t("filters.dateRange")}
            </p>
            <div className="flex gap-2">
              <DatePickerWithRange
                buttonClass="h-10 w-full"
                className="w-full h-10"
                date={date}
                setDate={(e: DateRange | undefined) =>
                  dispatch(
                    setDate(
                      e
                        ? {
                            from: e.from
                              ? new Date(e.from).toISOString()
                              : undefined,
                            to: e.to ? new Date(e.to).toISOString() : undefined,
                          }
                        : undefined
                    )
                  )
                }
              />
            </div>
          </div>
        </div>

        <DialogFooter className="px-5 pt-2 pb-0">
          <Button
            onClick={handleFilterContacts}
            className="w-full flex justify-center items-center gap-2 bg-foreground/70"
          >
            {loading ? (
              <>
                <CircularLoaderSmall />
                <span>{t("filters.searching")}</span>
              </>
            ) : (
              <>
                <SearchRoundedIcon />
                <span>{t("filters.search")}</span>
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export function FilterBadges() {
  const dispatch = useDispatch<AppDispatch>();
  const { t } = useTranslation("callLogs");
  const {
    callType,
    date,
    callSid,
    RecepientName,
    RecepientPhone,
    conversationID,
    conversationType,
    filtered,
  } = useSelector(
    (state: RootState) => state.businessDashboard.businessDashboardRoot
  );
  return (
    <div className="mb-3 flex flex-wrap gap-2">
      {callSid && (
        <FilterBadge
          label="SID"
          value={callSid}
          onRemove={() => dispatch(setCallSid(""))}
        />
      )}
      {conversationID && (
        <FilterBadge
          label="Conversation ID"
          value={conversationID}
          onRemove={() => dispatch(setConversationID(""))}
        />
      )}
      {RecepientName && (
        <FilterBadge
          label="Name"
          value={RecepientName}
          onRemove={() => dispatch(setRecepientName(""))}
        />
      )}
      {RecepientPhone && (
        <FilterBadge
          label="Phone"
          value={RecepientPhone}
          onRemove={() => dispatch(setRecepientPhone(""))}
        />
      )}
      {date?.from && (
        <FilterBadge
          label="Date"
          value={
            <>
              {format(date.from, "LLL dd, y")} - {format(date.to, "LLL dd, y")}
            </>
          }
          onRemove={() => dispatch(setDate(undefined))}
        />
      )}
      {Object.entries(conversationType || {}).map(
        ([type, isActive]) =>
          isActive && (
            <FilterBadge
              key={`conv-${type}`}
              label="Type"
              value={<span className="capitalize">{type}</span>}
              onRemove={() => {
                const newConversationType = { ...conversationType };
                newConversationType[type as keyof typeof newConversationType] =
                  false;
                dispatch(setConversationType(newConversationType));
              }}
            />
          )
      )}
      {Object.entries(callType || {}).map(
        ([type, isActive]) =>
          isActive && (
            <FilterBadge
              key={`call-${type}`}
              label="Call"
              value={<span className="capitalize">{type}</span>}
              onRemove={() => {
                const newCallType = { ...callType };
                newCallType[type as keyof typeof newCallType] = false;
                dispatch(setCallType(newCallType));
              }}
            />
          )
      )}
      {filtered && (
        <button
          onClick={() => dispatch(removeAllFilters())}
          className="flex items-center gap-1 bg-red-500/10 hover:bg-red-500/20 text-red-500 px-2 py-1 rounded-sm text-sm font-medium transition-colors"
        >
          {t("filters.clearAllFilters")}
        </button>
      )}
    </div>
  );
}

const FilterBadge = ({
  label,
  value,
  onRemove,
}: {
  label: string;
  value: string | React.ReactNode;
  onRemove: () => void;
}) => {
  const dispatch = useDispatch<AppDispatch>();

  const handleRemove = async () => {
    onRemove();
    dispatch(removeFilters());
    await dispatch(getConvertsationsByFilters());
  };

  return (
    <div className="px-2 py-1 text-sm flex items-center gap-1 rounded-sm bg-voxa-teal-500/10 text-voxa-teal-500">
      <span className="font-medium">{label}:</span>
      <span className="truncate max-w-[200px]">{value}</span>
      <button
        onClick={handleRemove}
        className="ml-1 hover:text-voxa-teal-600 dark:hover:text-voxa-teal-300"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>
  );
};
