import {
  DateRange,
  GetConversationsByFilters,
  GetConversationsByGoalID,
  GetEntrepriseConversationLogsStats,
  GetExportedConversations,
  addTagToConversation,
  removeTagFromConversation,
  setConversationLikedStatus,
} from "@/actions/ConversationActions";
import { CallStatusStat } from "@/types";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { toast } from "sonner";
import { BusinessDashboardState } from "../BusinessDashboardSlice";

const initialStatsState: CallStatusStat[] = [
  {
    titleKey: "stats.clientsNotReached",
    value: 0,
    icon: "PhoneX",
    color: "#EF4444",
    subStats: {
      missedCallsKey: "stats.missedCalls",
      missedCalls: 0,
      voicemailDetectedKey: "stats.voicemailDetected",
      voicemailDetected: 0,
    },
  },
  {
    titleKey: "stats.answeredNoTransfer",
    value: 0,
    icon: "PhoneOff",
    color: "#F59E0B",
    subStats: {
      notInterestedKey: "stats.notInterested",
      notInterested: 0,
      agentUnavailableKey: "stats.agentUnavailable",
      agentUnavailable: 0,
    },
  },
  {
    titleKey: "stats.transferredToAgents",
    value: 0,
    icon: "PhoneForwarded",
    color: "#3B82F6",
    subStats: {},
  },
  {
    titleKey: "stats.sentMessages",
    value: 0,
    icon: "MessageSquareText",
    color: "#10B981",
    subStats: {
      whatsAppKey: "stats.whatsApp",
      whatsApp: 0,
      smsKey: "stats.sms",
      sms: 0,
    },
  },
  {
    titleKey: "stats.voicemailSent",
    value: 0,
    icon: "Voicemail",
    color: "#8B5CF6",
    subStats: {},
  },
];

interface CallType {
  incoming: boolean;
  outgoing: boolean;
  missed: boolean;
  answered: boolean;
}

interface ConversationType {
  whatsapp: boolean;
  call: boolean;
  sms: boolean;
  meet:boolean;
}

interface LoadMore {
  loading: boolean;
  items: number;
  skip: number;
}

interface Tag {
  color: string;
  name: string;
}

interface RootState {
  callSid: string;
  RecepientName: string;
  RecepientPhone: string;
  date: any | undefined;
  conversationID: string;
  callType: CallType;
  conversationType: ConversationType;
  exportOpen: boolean;
  filtersOpen: boolean;
  NotesOpen: boolean;
  callDetails: any;
  callTranscriptOpen: boolean;
  stats: CallStatusStat[];
  loadMore: LoadMore;
  clientsFiltered: any;
  goalID: string;
  totalLogs: number | null;
  dateRange: DateRange;
  loading: boolean;
  calls: any[];
  filtered:boolean;
  isHistoryVisible:boolean;
  isTranscriptVisible:boolean;
}

const initialState: RootState = {
  callSid: "",
  RecepientName: "",
  RecepientPhone: "",
  conversationID: "",
  date: undefined,
  callType: {
    incoming: false,
    outgoing: false,
    missed: false,
    answered: false,
  },
  conversationType: {
    whatsapp: false,
    call: false,
    sms: false,
    meet:false
  },
  exportOpen: false,
  filtersOpen: false,
  NotesOpen: false,
  callDetails: null,
  callTranscriptOpen: false,
  stats: initialStatsState,
  loadMore: {
    loading: false,
    items: 5,
    skip: 0,
  },
  clientsFiltered: null,
  goalID: "",
  totalLogs: null,
  dateRange: {
    from: undefined,
    to: undefined,
  },
  loading: false,
  calls: [],
  filtered:false,
  isHistoryVisible:false,
  isTranscriptVisible:false
};

export const getExportedConversations = createAsyncThunk(
  "businessDashboardRoot/getExportedConversations",
  async (dateRange: DateRange, { dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));

      if (!dateRange.from || !dateRange.to) {
        return rejectWithValue("Please select a date range for export");
      }

      const response = await GetExportedConversations(dateRange);

      if (!response.success || !response.conversations) {
        return rejectWithValue(response.error || "Failed to get conversations");
      }

      return response.conversations;
    } catch (err: any) {
      console.error("Error in getExportedConversations:", err);
      return rejectWithValue(err.message || "An error occurred");
    } finally {
      dispatch(setLoading(false));
    }
  }
);

export const getConvertsationsByFilters = createAsyncThunk(
  "businessDashboardRoot/getConversationsByFilters",
  async (_, { dispatch, getState }) => {
    try {
      const state = getState() as { businessDashboard: BusinessDashboardState };

      const {
        callSid,
        RecepientName,
        RecepientPhone,
        date,
        callType,
        conversationID,
        conversationType,
      } = state.businessDashboard.businessDashboardRoot;
      dispatch(setLoading(true));

      if (
        !callSid &&
        !RecepientName &&
        !RecepientPhone &&
        !date &&
        !callType?.incoming &&
        !callType?.outgoing &&
        !callType?.missed &&
        !callType?.answered &&
        !conversationID &&
        !conversationType?.whatsapp &&
        !conversationType?.call &&
        !conversationType?.sms &&
        !conversationType?.meet
      ) {
        toast.error("At least one field is required.");
        return;
      }
      dispatch(setFiltered(true))
      const response = await GetConversationsByFilters(
        {
          CallSID: callSid,
          conversationID: conversationID,
          recepientName: RecepientName,
          recepientPhone: RecepientPhone,
          dateRange: date,
          incoming: callType?.incoming,
          outgoing: callType?.outgoing,
          missed: callType?.missed,
          answered: callType?.answered,
          Items: 10,
          skip: 0,
          whatsapp: conversationType?.whatsapp,
          sms: conversationType?.sms,
          call: conversationType?.call,
          meet: conversationType?.meet
        }
      );

      if (response.error) {
        toast.error(response.error);
        return;
      }

      if (response.conversations) {
        dispatch(setClientsFiltered(response.conversations));
        dispatch(setTotalLogs(response.totalCount || 0));
        if (response.loadedCount !== undefined) {
          dispatch(
            updateLoadMore({
              items: response.loadedCount,
              skip: response.loadedCount,
            })
          );
        }
        dispatch(setFiltersOpen(false));
      }
    } catch (err: any) {
      console.error(err);
      toast.error("Error while searching for logs");
    } finally {
      dispatch(setLoading(false));
    }
  }
);

export const fetchMoreConversations = createAsyncThunk(
  "businessDashboardRoot/fetchMoreConversations",
  async (_, { dispatch, getState }) => {
    try {
      const state = getState() as { businessDashboard: BusinessDashboardState };
      dispatch(updateLoadMore({ loading: true }));
      const {
        callSid,
        RecepientName,
        RecepientPhone,
        date,
        callType,
        loadMore,
        conversationID,
        conversationType,
      } = state.businessDashboard.businessDashboardRoot;

      if (
        !callSid &&
        !RecepientName &&
        !RecepientPhone &&
        !date &&
        !callType.incoming &&
        !callType.outgoing &&
        !callType.missed &&
        !callType.answered &&
        !conversationID &&
        !conversationType.whatsapp &&
        !conversationType.sms &&
        !conversationType.call &&
        !conversationType.meet
      ) {
        toast.error("At least one field is required.");
        return;
      }

      const response = await GetConversationsByFilters(
        {
          CallSID: callSid,
          conversationID: conversationID,
          recepientName: RecepientName,
          recepientPhone: RecepientPhone,
          dateRange: date,
          incoming: callType?.incoming,
          outgoing: callType?.outgoing,
          missed: callType?.missed,
          answered: callType?.answered,
          Items: 10,
          skip: 0,
          whatsapp: conversationType?.whatsapp,
          sms: conversationType?.sms,
          call: conversationType?.call,
          meet: conversationType?.meet
        }
      );

      if (response.error) {
        toast.error(response.error);
        return;
      }

      if (response.conversations) {
        dispatch(updateClientsFiltered(response.conversations));
        dispatch(setTotalLogs(response.totalCount || 0));
        if (response.loadedCount !== undefined) {
          dispatch(
            updateLoadMore({
              items: response.loadedCount,
              skip: response.loadedCount,
            })
          );
        }
      }

      dispatch(updateLoadMore({ loading: false, skip: loadMore.skip + 5 }));
    } catch (err) {
      console.error(err);
      toast.error("Error while searching for logs");
      dispatch(updateLoadMore({ loading: false }));
    }
  }
);

export const getCallLogsStats = createAsyncThunk(
  "businessDashboardRoot/getCallLogsStats",
  async (_, { dispatch }) => {
    try {
      const response = await GetEntrepriseConversationLogsStats();
      if (!response.success) {
        toast.error(response.error);
        return;
      }

      const stats = response.stats;
      if (!stats) {
        toast.error("No stats found");
        return;
      }
      dispatch(
        handleStatsChange({
          titleKey: "stats.clientsNotReached",
          value: stats.clientsNotReached.total,
          subStats: {
            missedCallsKey: "stats.missedCalls",
            missedCalls: stats.clientsNotReached.missedCalls,
            voicemailDetectedKey: "stats.voicemailDetected",
            voicemailDetected: stats.clientsNotReached.vmDetected,
          },
        })
      );

      dispatch(
        handleStatsChange({
          titleKey: "stats.answeredNoTransfer",
          value: stats.answeredNotForwarded.total,
          subStats: {
            notInterestedKey: "stats.notInterested",
            notInterested: stats.answeredNotForwarded.notInterested,
            agentUnavailableKey: "stats.agentUnavailable",
            agentUnavailable: stats.answeredNotForwarded.taNotAvailable,
          },
        })
      );

      dispatch(
        handleStatsChange({
          titleKey: "stats.transferredToAgents",
          value: stats.transferredToTA,
          subStats: {},
        })
      );

      dispatch(
        handleStatsChange({
          titleKey: "stats.sentMessages",
          value: stats.messageDueToMissed.total,
          subStats: {
            whatsAppKey: "stats.whatsApp",
            whatsApp: stats.messageDueToMissed.whatsapp,
            smsKey: "stats.sms",
            sms: stats.messageDueToMissed.sms,
          },
        })
      );

      dispatch(
        handleStatsChange({
          titleKey: "stats.voicemailSent",
          value: stats.voicemailsSent,
          subStats: {},
        })
      );
    } catch (err: any) {
      console.error(err);
      toast.error("Error while fetching stats");
    }
  }
);

export const findConversationsByGoalID = createAsyncThunk(
  "businessDashboardRoot/findConversationsByGoalID",
  async (goalID: string, { dispatch }) => {
    try {
      const response = await GetConversationsByGoalID(goalID);
      if (!response.success) {
        toast.error("No conversations found for this goal ID");
        return;
      }
      if (response.conversations) {
        dispatch(setClientsFiltered(response.conversations));
        dispatch(setTotalLogs(response.total));
      }
    } catch (err: any) {
      toast.error(err.message);
    }
  }
);

export const addTagToCallDetails = createAsyncThunk(
  "businessDashboardRoot/addTagToCallDetails",
  async (
    {
      conversationId,
      tagName,
      tagColor,
    }: { conversationId: string; tagName: string; tagColor: string },
    { dispatch, rejectWithValue, getState }
  ) => {
    const state = getState() as { businessDashboard: BusinessDashboardState };
    const { clientsFiltered, calls } =
      state.businessDashboard.businessDashboardRoot;
    try {
      const response = await addTagToConversation(
        conversationId,
        tagName,
        tagColor
      );
      if (!response.success) {
        toast.error("Failed to add tag");
        return rejectWithValue(response.error || "Failed to add tag");
      }
      dispatch(updateCallDetailsTags(response.tags as Tag[]));

      if (calls && Array.isArray(calls)) {
        const updatedCalls = calls.map((call: any) =>
          call._id === conversationId ? { ...call, tags: response.tags } : call
        );
        dispatch(setCalls(updatedCalls));
      }
      if (clientsFiltered && Array.isArray(clientsFiltered)) {
        const updatedClientsFiltered = clientsFiltered.map((call: any) =>
          call._id === conversationId ? { ...call, tags: response.tags } : call
        );
        dispatch(setClientsFiltered(updatedClientsFiltered));
      }
      console.log(
        "Clients Filtered:" + JSON.stringify(clientsFiltered, null, 2)
      );
      return response.tags;
    } catch (err: any) {
      toast.error("Failed to add tag");
      return rejectWithValue(err.message || "An error occurred");
    }
  }
);

export const removeTagFromCallDetails = createAsyncThunk(
  "businessDashboardRoot/removeTagFromCallDetails",
  async (
    {
      conversationId,
      tagName,
      tagColor,
    }: { conversationId: string; tagName: string; tagColor: string },
    { dispatch, rejectWithValue, getState }
  ) => {
    const state = getState() as { businessDashboard: BusinessDashboardState };
    const { clientsFiltered, calls } =
      state.businessDashboard.businessDashboardRoot;
    try {
      const response = await removeTagFromConversation(
        conversationId,
        tagName,
        tagColor
      );
      if (!response.success) {
        toast.error("Failed to remove tag");
        return rejectWithValue(response.error || "Failed to remove tag");
      }
      dispatch(updateCallDetailsTags(response.tags as Tag[]));

      if (calls && Array.isArray(calls)) {
        const updatedCalls = calls.map((call: any) =>
          call._id === conversationId ? { ...call, tags: response.tags } : call
        );
        dispatch(setCalls(updatedCalls));
      }
      if (clientsFiltered && Array.isArray(clientsFiltered)) {
        const updatedClientsFiltered = clientsFiltered.map((call: any) =>
          call._id === conversationId ? { ...call, tags: response.tags } : call
        );
        dispatch(setClientsFiltered(updatedClientsFiltered));
      }
      console.log(
        "Clients Filtered:" + JSON.stringify(clientsFiltered, null, 2)
      );
      return response.tags;
    } catch (err: any) {
      toast.error("Failed to remove tag");
      return rejectWithValue(err.message || "An error occurred");
    }
  }
);

export const setConversationLiked = createAsyncThunk(
  "businessDashboardRoot/setConversationLiked",
  async (
    {
      conversationId,
      isLiked,
    }: { conversationId: string; isLiked: boolean | null },
    { dispatch, rejectWithValue, getState }
  ) => {
    const state = getState() as { businessDashboard: BusinessDashboardState };
    const { calls, callDetails, clientsFiltered, filtered } =
      state.businessDashboard.businessDashboardRoot;
    let prevCallDetails: any = null;
    let prevClientsFiltered: any = null;

    if (callDetails && callDetails._id === conversationId) {
      prevCallDetails = { ...callDetails };
      dispatch({
        type: "businessDashboardRoot/handleOpenCallDetails",
        payload: { ...callDetails, isLiked },
      });
    }
    if (calls && Array.isArray(calls)) {
      const updatedCalls = calls.map((call: any) =>
        call._id === conversationId ? { ...call, isLiked } : call
      );
      dispatch(setCalls(updatedCalls));
    }
    if (filtered && clientsFiltered && Array.isArray(clientsFiltered)) {
      prevClientsFiltered = [...clientsFiltered];
      const updatedClientsFiltered = clientsFiltered.map((call: any) =>
        call._id === conversationId ? { ...call, isLiked } : call
      );
      dispatch(setClientsFiltered(updatedClientsFiltered));
    }
    try {
      const response = await setConversationLikedStatus(
        conversationId,
        isLiked
      );
      if (!response.success) {
        if (prevCallDetails) {
          dispatch({
            type: "businessDashboardRoot/handleOpenCallDetails",
            payload: prevCallDetails,
          });
        }
        if (filtered && prevClientsFiltered) {
          dispatch(setClientsFiltered(prevClientsFiltered));
        }
        return rejectWithValue(response.error || "Failed to set like status");
      }
      return { conversationId, isLiked };
    } catch (err: any) {
      if (prevCallDetails) {
        dispatch({
          type: "businessDashboardRoot/handleOpenCallDetails",
          payload: prevCallDetails,
        });
      }
      if (filtered && prevClientsFiltered) {
        dispatch(setClientsFiltered(prevClientsFiltered));
      }
      return rejectWithValue(err.message || "An error occurred");
    }
  }
);

const rootSlice = createSlice({
  name: "businessDashboardRoot",
  initialState,
  reducers: {
    setClientsFiltered(state, action: PayloadAction<any>) {
      state.clientsFiltered = action.payload;
    },
    updateClientsFiltered(state, action) {
      if (state.clientsFiltered) {
        state.clientsFiltered = [...state.clientsFiltered, ...action.payload];
      } else {
        state.clientsFiltered = [...action.payload];
      }
    },
    updateLoadMore(state, action) {
      state.loadMore = { ...state.loadMore, ...action.payload };
    },
    handleStatsChange(state, action: PayloadAction<any>) {
      const newStats = state.stats.map((stat: any) => {
        if (stat.titleKey === action.payload.titleKey) {
          return {
            ...stat,
            value: action.payload.value,
            subStats: action.payload.subStats ?? stat.subStats,
          };
        }
        return stat;
      });
      state.stats = newStats;
    },
    handleTranscriptionOpen(state, action: PayloadAction<any>) {
      state.callDetails = action.payload;
      state.callTranscriptOpen = true;
    },
    handleCloseTranscript(state) {
      state.callDetails = null;
      state.callTranscriptOpen = false;
    },
    handleOpenCallDetails(state, action: PayloadAction<any | null>) {
        state.callDetails = action.payload;
        state.NotesOpen = true;
    },
    handleCloseCallDetails(state) {
      state.callDetails = null;
      state.NotesOpen = false;
    },
    removeFilters(state) {
      state.callDetails = null;
      state.NotesOpen = false;
      state.clientsFiltered = [];
      if (!state.callSid &&
        !state.RecepientName &&
        !state.RecepientPhone &&
        !state.date &&
        !state.callType?.incoming &&
        !state.callType?.outgoing &&
        !state.callType?.missed &&
        !state.callType?.answered &&
        !state.conversationID &&
        !state.conversationType?.whatsapp &&
        !state.conversationType?.call &&
        !state.conversationType?.sms &&
        !state.conversationType?.meet){
          state.filtered = false
        }
    },
    removeAllFilters(state) {
      state.filtered = false;
      state.callSid = "";
      state.RecepientName = "";
      state.RecepientPhone = "";
      state.date = undefined;
      state.callType = {
        incoming: false,
        outgoing: false,
        missed: false,
        answered: false,
      };
      state.conversationID = "";
      state.conversationType = {
        whatsapp: false,
        call: false,
        sms: false,
        meet:false
      };
      state.callDetails = null;
      state.NotesOpen = false;
      state.clientsFiltered = [];
    },
    setDate: (state, action: PayloadAction<any | null>) => {
      state.date = action.payload;
    },
    setCallType(state, action: PayloadAction<CallType>) {
      state.callType = action.payload;
    },
    setConversationType(state, action: PayloadAction<ConversationType>) {
      state.conversationType = action.payload;
    },
    setGoalID(state, action: PayloadAction<string>) {
      state.goalID = action.payload;
    },
    setExportOpen(state, action: PayloadAction<boolean>) {
      state.exportOpen = action.payload;
    },
    setFiltersOpen(state, action: PayloadAction<boolean>) {
      state.filtersOpen = action.payload;
    },
    setNotesOpen(state, action: PayloadAction<boolean>) {
      state.NotesOpen = action.payload;
    },
    setCallTranscriptOpen(state, action: PayloadAction<boolean>) {
      state.callTranscriptOpen = action.payload;
    },
    setCallSid(state, action: PayloadAction<string>) {
      state.callSid = action.payload;
    },
    setRecepientName(state, action: PayloadAction<string>) {
      state.RecepientName = action.payload;
    },
    setRecepientPhone(state, action: PayloadAction<string>) {
      state.RecepientPhone = action.payload;
    },
    setLoadMore(state, action: PayloadAction<LoadMore>) {
      state.loadMore = action.payload;
    },
    setTotalLogs(state, action: PayloadAction<number>) {
      state.totalLogs = action.payload;
    },
    setConversationID(state, action: PayloadAction<string>) {
      state.conversationID = action.payload;
    },
    setDateRange(state, action: PayloadAction<DateRange>) {
      state.dateRange = action.payload;
    },
    setLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },
    updateCallDetailsNotes(state, action: PayloadAction<{ conversationId: string; notes: string }>) {
      const { conversationId, notes } = action.payload;
      if (state.callDetails && state.callDetails._id === conversationId) {
        state.callDetails.notes = notes;
      }
      if (state.calls && Array.isArray(state.calls)) {
        state.calls = state.calls.map((call) =>
          call._id === conversationId ? { ...call, notes } : call
        );
      }
      if (state.clientsFiltered && Array.isArray(state.clientsFiltered)) {
        state.clientsFiltered = state.clientsFiltered.map((call) =>
          call._id === conversationId ? { ...call, notes } : call
        );
      }
    },
    updateCallDetailsTags(state, action: PayloadAction<any[]>) {
      if (state.callDetails) {
        state.callDetails.tags = action.payload;
      }
    },
    setCalls(state, action: PayloadAction<any[]>) {
      state.calls = action.payload;
    },
    updateCalls(state, action: PayloadAction<any[]>) {
      state.calls = [...state.calls, ...action.payload];
    },
    setCallDetails(state, action: PayloadAction<any | null>) {
      state.callDetails = action.payload;
    },
    setFiltered(state, action: PayloadAction<boolean>) {
      state.filtered = action.payload;
    },
    setIsHistoryVisible(state, action: PayloadAction<boolean>) {
      state.isHistoryVisible = action.payload;
    },
    setIsTranscriptVisible(state, action: PayloadAction<boolean>) {
      state.isTranscriptVisible = action.payload;
    },
  },
});

export const {
  setClientsFiltered,
  updateClientsFiltered,
  updateLoadMore,
  handleOpenCallDetails,
  handleStatsChange,
  handleCloseCallDetails,
  handleTranscriptionOpen,
  removeFilters,
  removeAllFilters,
  setDate,
  setCallType,
  setConversationType,
  setGoalID,
  setExportOpen,
  setFiltersOpen,
  setNotesOpen,
  setCallTranscriptOpen,
  setCallSid,
  setLoadMore,
  setRecepientName,
  setRecepientPhone,
  setTotalLogs,
  setConversationID,
  setDateRange,
  setLoading,
  updateCallDetailsNotes,
  updateCallDetailsTags,
  setCalls,
  updateCalls,
  handleCloseTranscript,
  setFiltered,
  setIsHistoryVisible,
  setIsTranscriptVisible,
  setCallDetails
} = rootSlice.actions;

export default rootSlice.reducer;
