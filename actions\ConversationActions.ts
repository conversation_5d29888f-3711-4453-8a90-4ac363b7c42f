"use server";

import dbConnect from "@/lib/mongodb";
import { getEntrepriseByAdminID } from "./Entreprise";
import Assistant from "@/models/Assistant";
import Conversation from "@/models/Conversation";
import Goal from "@/models/Goal";
import mongoose from "mongoose";
import { startOfDay, endOfDay, addHours } from "date-fns";
import { DateTime } from "luxon";

export interface DateRange {
  from: string | undefined;
  to: string | undefined;
}

export async function GetExportedConversations(dateRange: DateRange): Promise<{
  success: boolean;
  error?: string;
  conversations?: {
    _id: string;
    TimeStamp: string;
    To: string;
    CallStatus: string;
    client_name: string;
    voicemail_detected: boolean;
    conversation_count: number;
  }[];
}> {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }
    await dbConnect();
    const entrepriseID = entrepriseResponse.entreprise._id;

    if (!dateRange.from || !dateRange.to) {
      return {
        success: false,
        error: "Please provide both start and end dates for the range.",
      };
    }

    let fromDate: any = dateRange?.from ? new Date(dateRange.from) : undefined;
    let toDate: any = dateRange?.to ? new Date(dateRange.to) : undefined;

    fromDate = fromDate ? addHours(startOfDay(fromDate), 1) : undefined;
    toDate = toDate ? endOfDay(toDate) : undefined;

    console.log("From Date:", fromDate);
    console.log("To Date:", toDate);

    const matchCondition = {
      entreprise_id: entrepriseID,
      $expr: {
        $and: [
          {
            $gte: [
              { $dateFromString: { dateString: "$initiated_time" } },
              fromDate,
            ],
          },
          {
            $lt: [
              { $dateFromString: { dateString: "$initiated_time" } },
              toDate,
            ],
          },
        ],
      },
      $or: [{ voicemail_detected: true }, { status: "MISSED" }],
    };

    const conversations = await Conversation.aggregate([
      { $match: matchCondition },
      {
        $addFields: {
          timestampAsDate: {
            $dateFromString: {
              dateString: "$initiated_time",
            },
          },
        },
      },
      {
        $sort: { timestampAsDate: -1 },
      },
      {
        $group: {
          _id: "$to_number",
          latestConversation: { $first: "$$ROOT" },
          conversation_count: { $sum: 1 },
        },
      },
      {
        $project: {
          _id: "$latestConversation._id",
          TimeStamp: "$latestConversation.initiated_time",
          To: "$latestConversation.to_number",
          CallStatus: "$latestConversation.status",
          client_name: "$latestConversation.to_name",
          voicemail_detected: "$latestConversation.voicemail_detected",
          conversation_count: 1,
        },
      },
    ]);

    if (!conversations.length) {
      return {
        success: false,
        error:
          "No missed calls or voicemail detected conversations found in the selected date range.",
      };
    }

    const formattedConversations = conversations.map((conv) => ({
      _id: conv._id.toString(),
      TimeStamp: conv.initiated_time || "",
      To: conv.To || "",
      CallStatus: conv.CallStatus || "",
      client_name: conv.client_name || "",
      voicemail_detected: conv.voicemail_detected || false,
      conversation_count: conv.conversation_count || 0,
    }));

    return { success: true, conversations: formattedConversations };
  } catch (e: any) {
    console.error("Error in GetExportedConversations:", e);
    return { success: false, error: e.message };
  }
}

export const GetConversationsByToNumber = async (
  conversationID: string,
  To: string,
  limit?: number,
  skip?: number
): Promise<{
  success: boolean;
  error?: string;
  conversations?: any;
  totalCount?: number;
}> => {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }

    await dbConnect();
    const entrepriseID = entrepriseResponse.entreprise._id;

    const query: any = {
      entreprise_id: entrepriseID,
      to_number: To,
      _id: { $ne: new mongoose.Types.ObjectId(conversationID) },
    };

    const totalCount = await Conversation.countDocuments(query);

    let conversationsQuery = Conversation.find(query)
      .sort({ initiated_time: -1 })
      .lean();

    if (typeof skip !== "undefined") {
      conversationsQuery = conversationsQuery.skip(skip);
    }

    if (typeof limit !== "undefined") {
      conversationsQuery = conversationsQuery.limit(limit);
    }
    const conversations = await conversationsQuery;
    const formattedConversations = conversations.map((conv) => ({
      _id: conv._id.toString(),
      CallSid: conv.sid || "",
      From: conv.from_number || "",
      TimeStamp: conv.initiated_time || "",
      Direction: conv.direction || "",
      CallDuration: conv.duration || 0,
      voicemailDetected: conv.voicemail_detected || false,
      Country: conv.from_country || "",
      is_transfer: conv.direction?.toLowerCase() === "outbound-dial",
      is_voicemail_drop: conv.is_voicemail_drop || false,
      To: conv.to_number || "",
      CallStatus: conv.status || "",
      Type: conv.type || "",
      notes: conv.notes || "",
    }));

    return {
      success: true,
      conversations: formattedConversations,
      totalCount,
    };
  } catch (e: any) {
    return { success: false, error: e.message };
  }
};

export const GetConversationHistory = async (
  daysAgo: number,
  timezone: string,
  limit: number = 5,
  skip: number = 0
): Promise<{
  success: boolean;
  error?: string;
  conversations?: any[];
  totalCount?: number;
  loadedCount?: number;
}> => {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }
    await dbConnect();
    const entrepriseID = entrepriseResponse.entreprise._id;

    const gmtPlus2Time = DateTime.now()
      .setZone(timezone || "UTC")
      .minus({ days: daysAgo === 4 ? daysAgo - 1 : daysAgo })
      .startOf("day");
    const targetDate = gmtPlus2Time.toISO();
    const nextDayDate = gmtPlus2Time.plus({ days: 1 }).toISO();
    const matchCondition = {
      entreprise_id: entrepriseID,
      ...(daysAgo === 4
        ? {
            $expr: {
              $lte: [
                { $dateFromString: { dateString: "$initiated_time" } },
                { $dateFromString: { dateString: targetDate } },
              ],
            },
          }
        : {
            $expr: {
              $and: [
                {
                  $gte: [
                    { $dateFromString: { dateString: "$initiated_time" } },
                    { $dateFromString: { dateString: targetDate } },
                  ],
                },
                {
                  $lt: [
                    { $dateFromString: { dateString: "$initiated_time" } },
                    { $dateFromString: { dateString: nextDayDate } },
                  ],
                },
              ],
            },
          }),
    };
    const countResult = await Conversation.aggregate([
      { $match: matchCondition },
      { $count: "total" },
    ]);
    const totalCount = countResult.length > 0 ? countResult[0].total : 0;
    const conversations = await Conversation.aggregate([
      { $match: matchCondition },
      {
        $addFields: {
          timestampAsDate: {
            $dateFromString: {
              dateString: "$initiated_time",
            },
          },
        },
      },
      { $sort: { timestampAsDate: -1 } },
      { $skip: skip },
      { $limit: limit },
    ]);

    if (!conversations.length) {
      return {
        success: true,
        conversations: [],
        totalCount: 0,
        loadedCount: 0,
      };
    }

    const enrichedConversations = conversations.map((conversation) => ({
      _id: conversation._id.toString(),
      CallSid: conversation.sid || "",
      TimeStamp: conversation.initiated_time || "",
      Direction: conversation.direction || "",
      RecordingSid: conversation.recording_sid || "",
      From:
        conversation.direction?.toLowerCase() === "outbound-dial"
          ? conversation.forwarded_from
          : conversation.from_number || "",
      To:
        conversation.direction?.toLowerCase() === "outbound-dial"
          ? conversation.to_number || ""
          : conversation.to_number || "",
      CallerCountry: conversation.from_country || "",
      ToCity: conversation.to_city || "",
      CallStatus: conversation.status || "",
      CallDuration: conversation.duration ? conversation.duration : 0,
      assistant_name: conversation.assistant_name || "",
      client_name: conversation.to_name || "",
      goalId: conversation.goal_id?.toString() || null,
      assistantId: conversation.assistant_id?.toString() || null,
      entreprise: conversation.entreprise_id?.toString() || "",
      batch_transcript_entities:
        conversation.batch_transcription?.entities || [],
      batch_transcript_utterances:
        conversation.batch_transcription?.utterances || [],
      transcript:
        conversation.direction === "outbound-api"
          ? parseInt(conversation.duration as string) > 10
            ? conversation.merged_transcript || []
            : []
          : conversation.direction === "inbound"
          ? conversation.merged_transcript
          : [],
      client: conversation.to_client_id?.toString() || "",
      feelings:
        parseInt(conversation.duration as string) > 10
          ? conversation.feelings || "No feelings."
          : "No feeling for conversations under 10 seconds.",
      summary:
        parseInt(conversation.duration as string) > 10
          ? conversation.summary || "No summary."
          : "No summary for conversations under 10 seconds.",
      voicemailDetected: conversation.voicemail_detected || false,
      who_hang_up: conversation.who_hang_up || "",
      RecordingURL: conversation.recording_url || "",
      RecordingStatus: conversation.recording_status || "",
      recording_duration: conversation.recording_duration || "",
      initiated_time: conversation.initiated_time || "",
      ringing_time: conversation.ringing_time || "",
      inprogress_time: conversation.inprogress_time || "",
      completed_time: conversation.completed_time || "",
      forwarded_time: conversation.forwarded_time || "",
      child_conversation_id: conversation.child_id?.toString() || "",
      child_name: conversation.child_name || "",
      child_call_sid: conversation.child_sid || "",
      child_type: conversation.child_type || "",
      parent_call_sid: conversation.parent_sid || "",
      forward_start_time_in_audio:
        conversation.forward_start_time_in_audio || "",
      is_transfer:
        conversation.direction?.toLowerCase() === "outbound-dial"
          ? true
          : false,
      is_voicemail_drop: conversation.is_voicemail_drop || false,
      body: conversation.body || "",
      type: conversation.type || "",
      notes: conversation.notes || "",
      isLiked: conversation.isLiked ?? null,
      tags: conversation.tags || [],
      clientEngagement: conversation.client_engagement || 0,
      most_talkative_speaker_name:
        conversation.most_talkative_speaker_name || "",
      total_speaking_time_seconds:
        conversation.total_speaking_time_seconds || 0,
      speaker_durations_list: conversation.speaker_durations_list || [],
    }));
    const loadedCount = skip + enrichedConversations.length;
    return {
      success: true,
      conversations: enrichedConversations,
      totalCount,
      loadedCount: Math.min(loadedCount, totalCount),
    };
  } catch (e: any) {
    console.log("Error in getting conversations:", e.message);
    return { success: false, error: e.message };
  }
};

export const CreateNewConversation = async () => {
  await dbConnect();

  const newConversation = new Conversation({
    content: "content",
    assistant: "67b761222d31b5f9e858b36c",
    client: "67b65ce66367436edf656be9",
  });

  const savedConversation = await newConversation.save();

  await Assistant.findByIdAndUpdate(
    "assistantId",
    { $push: { conversations: savedConversation._id } },
    { new: true }
  );
};

export async function GetConversationHistoryByUserID(
  userid: string
): Promise<{ success: boolean; error?: string; conversations?: any[] }> {
  try {
    await dbConnect();
    const conversations = await Conversation.find({
      assistantId: userid,
    }).lean();
    return { success: true, conversations };
  } catch (e: any) {
    return { success: false, error: e.message };
  }
}

interface GetConversationsByFiltersProps {
  entreprise_id?: string,
  CallSID?: string,
  conversationID?: string,
  recepientName?: string,
  recepientPhone?: string,
  dateRange?: any,
  incoming?: boolean,
  outgoing?: boolean,
  missed?: boolean,
  answered?: boolean,
  Items?: number,
  skip?: number,
  whatsapp?: boolean,
  sms?: boolean,
  call?: boolean,
  meet?: boolean
}

export async function GetConversationsByFilters(
  {
  entreprise_id,
  CallSID,
  conversationID,
  recepientName,
  recepientPhone,
  dateRange,
  incoming,
  outgoing,
  missed,
  answered,
  Items,
  skip,
  whatsapp,
  sms,
  call,
  meet
  } : GetConversationsByFiltersProps
): Promise<{
  success: boolean;
  error?: string;
  conversations?: any[];
  totalCount?: number;
  loadedCount?: number;
}> {
  try {
    let entrepriseObjectID: any;
    if(!entreprise_id){
      const entrepriseResponse = await getEntrepriseByAdminID();
      if (!entrepriseResponse.success) {
        return { success: false, error: entrepriseResponse.error };
      }
      entrepriseObjectID = entrepriseResponse.entreprise._id
    }else{
      entrepriseObjectID = new mongoose.Types.ObjectId(entreprise_id)
    }
    await dbConnect();
    let conversationObjectID: any;
    if (conversationID) {
      conversationObjectID = new mongoose.Types.ObjectId(conversationID);
    }
    let fromDate: any = dateRange?.from ? new Date(dateRange.from) : undefined;
    let toDate: any = dateRange?.to ? new Date(dateRange.to) : undefined;

    fromDate = fromDate ? addHours(startOfDay(fromDate), 1) : undefined;
    toDate = toDate ? endOfDay(toDate) : undefined;

    const matchStage: any = {
      ...(conversationObjectID && { _id: conversationObjectID }),
      entreprise_id: entrepriseObjectID,
      ...(CallSID && { sid: CallSID }),
      ...(whatsapp || sms || call || meet
        ? {
            $or: [
              ...(whatsapp ? [{ type: "WHATSAPP" }] : []),
              ...(sms ? [{ type: "SMS" }] : []),
              ...(call ? [{ type: "CALL" }] : []),
              ...(meet ? [{ type: "MEET" }] : []),
            ],
          }
        : {}),
      ...(fromDate &&
        toDate && {
          parsedTimeStamp: {
            $gte: fromDate,
            $lte: toDate,
          },
        }),

      ...(fromDate &&
        !toDate && {
          parsedTimeStamp: {
            $gte: fromDate,
            $lte: addHours(endOfDay(fromDate), 1),
          },
        }),

      ...(toDate &&
        !fromDate && {
          parsedTimeStamp: {
            $gte: addHours(endOfDay(toDate), 1),
            $lte: toDate,
          },
        }),

      ...(incoming || outgoing || missed || answered
        ? {
            $or: [
              ...(incoming ? [{ direction: "inbound" }] : []),
              ...(outgoing ? [{ direction: "outbound-api" }] : []),
              ...(missed ? [{ type: "CALL", duration: "0" }] : []),
              ...(answered
                ? [{ status: "completed", duration: { $gt: 0 } }]
                : []),
            ],
          }
        : {}),

      ...(recepientName && {
        to_name: { $regex: new RegExp(recepientName, "i") },
      }),

      ...(recepientPhone && {
        to_number: {
          $regex: new RegExp(
            recepientPhone.replace(/[.*+?^=!:${}()|\[\]\/\\]/g, "\\$&"),
            "i"
          ),
        },
      }),
    };

    const pipeline: any = [
      {
        $addFields: {
          parsedTimeStamp: {
            $dateFromString: { dateString: "$initiated_time" },
          },
        },
      },
      {
        $match: matchStage,
      },
      {
        $facet: {
          totalCount: [{ $count: "count" }],
          conversations: [
            { $sort: { parsedTimeStamp: -1 } },
            { $skip: skip || 0 },
            { $limit: Items || 5 },
          ],
        },
      },
    ];

    const result = await Conversation.aggregate(pipeline);

    const totalCount = result[0].totalCount.length
      ? result[0].totalCount[0].count
      : 0;
    const conversations = result[0].conversations;
    const enrichedConversations = conversations.map((conversation: any) => ({
      _id: conversation._id.toString(),
      CallSid: conversation.sid || "",
      TimeStamp: conversation.initiated_time || "",
      Direction: conversation.direction || "",
      RecordingSid: conversation.recording_sid || "",
      From:
        conversation.direction?.toLowerCase() === "outbound-dial"
          ? conversation.forwarded_from
          : conversation.from_number || "",
      To:
        conversation.direction?.toLowerCase() === "outbound-dial"
          ? conversation.to_number || ""
          : conversation.to_number || "",
      CallerCountry: conversation.from_country || "",
      ToCity: conversation.to_city || "",
      CallStatus: conversation.status || "",
      CallDuration: conversation.duration ? conversation.duration : 0,
      assistant_name: conversation.assistant_name || "",
      client_name: conversation.to_name || "",
      goalId: conversation.goal_id?.toString() || null,
      assistantId: conversation.assistant_id?.toString() || null,
      entreprise: conversation.entreprise_id?.toString() || "",
      batch_transcript_entities:
        conversation.batch_transcription?.entities || [],
      batch_transcript_utterances:
        conversation.batch_transcription?.utterances || [],
      transcript:
        conversation.direction === "outbound-api"
          ? parseInt(conversation.duration as string) > 10
            ? conversation.merged_transcript || []
            : []
          : conversation.direction === "inbound"
          ? conversation.merged_transcript
          : [],
      client: conversation.to_client_id?.toString() || "",
      feelings:
        parseInt(conversation.duration as string) > 10
          ? conversation.feelings || "No feelings."
          : "No feeling for conversations under 10 seconds.",
      summary:
        parseInt(conversation.duration as string) > 10
          ? conversation.summary || "No summary."
          : "No summary for conversations under 10 seconds.",
      voicemailDetected: conversation.voicemail_detected || false,
      who_hang_up: conversation.who_hang_up || "",
      RecordingURL: conversation.recording_url || "",
      RecordingStatus: conversation.recording_status || "",
      recording_duration: conversation.recording_duration || "",
      initiated_time: conversation.initiated_time || "",
      ringing_time: conversation.ringing_time || "",
      inprogress_time: conversation.inprogress_time || "",
      completed_time: conversation.completed_time || "",
      forwarded_time: conversation.forwarded_time || "",
      child_conversation_id: conversation.child_id?.toString() || "",
      child_name: conversation.child_name || "",
      child_call_sid: conversation.child_sid || "",
      child_type: conversation.child_type || "",
      parent_call_sid: conversation.parent_sid || "",
      forward_start_time_in_audio:
        conversation.forward_start_time_in_audio || "",
      is_voicemail_drop: conversation.is_voicemail_drop || false,
      is_transfer:
        conversation.direction?.toLowerCase() === "outbound-dial"
          ? true
          : false,
      answered_and_vm_detected:
        conversation.direction?.toLowerCase() === "outbound-api" &&
        conversation.type.toLowerCase() === "call" &&
        conversation.status.toLowerCase() === "answered" &&
        conversation.voicemail_detected === true &&
        conversation.inprogress_time &&
        conversation.ringing_time &&
        (new Date(conversation.inprogress_time).getTime() -
          new Date(conversation.ringing_time).getTime()) /
          1000 >=
          10
          ? true
          : false,
      missed_and_vm_dropped:
        conversation.direction?.toLowerCase() === "outbound-api" &&
        conversation.type.toLowerCase() === "call" &&
        conversation.status.toLowerCase() === "missed" &&
        conversation.voicemail_detected === true
          ? true
          : false,
      body: conversation.body || "",
      type: conversation.type || "",
      notes: conversation.notes || "",
      isLiked: conversation.isLiked ?? null,
      tags: conversation.tags || [],
      clientEngagement: conversation.client_engagement || 0,
      most_talkative_speaker_name:
        conversation.most_talkative_speaker_name || "",
      total_speaking_time_seconds:
        conversation.total_speaking_time_seconds || 0,
      speaker_durations_list: conversation.speaker_durations_list || [],
    }));
    const loadedCount = (skip || 0) + enrichedConversations.length;
    console.log("Enriched Conversations:", enrichedConversations);
    return {
      success: true,
      conversations: enrichedConversations.sort(
        (a: any, b: any) =>
          new Date(b.initiated_time as string).getTime() -
          new Date(a.initiated_time as string).getTime()
      ),
      totalCount,
      loadedCount: Math.min(loadedCount, totalCount),
    };
  } catch (e: any) {
    console.log("Error in getting filtered conversations:", e.message);
    return { success: false, error: e.message };
  }
}

export async function GetConversationsByGoalID(goalID: string) {
  try {
    if (!mongoose.Types.ObjectId.isValid(goalID)) {
      return { success: false, error: "Invalid Goal ID format." };
    }

    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }

    await dbConnect();

    const goal = await Goal.findOne({
      _id: goalID,
      entreprise: entrepriseResponse.entreprise._id,
    }).populate("conversations");

    if (!goal || !goal.conversations || goal.conversations.length === 0) {
      return {
        success: false,
        error: "No Conversations found for this Goal ID.",
      };
    }

    const sortedConversations = goal.conversations.sort((a: any, b: any) => {
      const timestampA = a.initiated_time ? Date.parse(a.initiated_time) : 0;
      const timestampB = b.initiated_time ? Date.parse(b.initiated_time) : 0;
      return timestampB - timestampA;
    });

    const conversations = sortedConversations.map((conversation: any) => {
      const { _id } = conversation.toObject();

      return {
        _id: conversation._id.toString(),
        CallSid: conversation.sid || "",
        TimeStamp: conversation.initiated_time || "",
        Direction: conversation.direction || "",
        RecordingSid: conversation.recording_sid || "",
        From:
          conversation.direction?.toLowerCase() === "outbound-dial"
            ? conversation.forwarded_from
            : conversation.from_number || "",
        To:
          conversation.direction?.toLowerCase() === "outbound-dial"
            ? conversation.to_number || ""
            : conversation.to_number || "",
        CallerCountry: conversation.from_country || "",
        ToCity: conversation.to_city || "",
        CallStatus: conversation.status || "",
        CallDuration: conversation.duration ? conversation.duration : 0,
        assistant_name: conversation.assistant_name || "",
        client_name: conversation.to_name || "",
        goalId: conversation.goal_id?.toString() || null,
        assistantId: conversation.assistant_id?.toString() || null,
        entreprise: conversation.entreprise_id?.toString() || "",
        batch_transcript_entities:
          conversation.batch_transcription?.entities || [],
        batch_transcript_utterances:
          conversation.batch_transcription?.utterances || [],
        transcript:
          conversation.direction === "outbound-api"
            ? parseInt(conversation.duration as string) > 10
              ? conversation.merged_transcript || []
              : []
            : conversation.direction === "inbound"
            ? conversation.merged_transcript
            : [],
        client: conversation.to_client_id?.toString() || "",
        feelings:
          parseInt(conversation.duration as string) > 10
            ? conversation.feelings || "No feelings."
            : "No feeling for conversations under 10 seconds.",
        summary:
          parseInt(conversation.duration as string) > 10
            ? conversation.summary || "No summary."
            : "No summary for conversations under 10 seconds.",
        voicemailDetected: conversation.voicemail_detected || false,
        who_hang_up: conversation.who_hang_up || "",
        RecordingURL: conversation.recording_url || "",
        RecordingStatus: conversation.recording_status || "",
        recording_duration: conversation.recording_duration || "",
        initiated_time: conversation.initiated_time || "",
        ringing_time: conversation.ringing_time || "",
        inprogress_time: conversation.inprogress_time || "",
        completed_time: conversation.completed_time || "",
        forwarded_time: conversation.forwarded_time || "",
        child_conversation_id: conversation.child_id?.toString() || "",
        child_name: conversation.child_name || "",
        child_call_sid: conversation.child_sid || "",
        child_type: conversation.child_type || "",
        parent_call_sid: conversation.parent_sid || "",
        forward_start_time_in_audio:
          conversation.forward_start_time_in_audio || "",
        is_voicemail_drop: conversation.is_voicemail_drop || false,
        is_transfer:
          conversation.direction?.toLowerCase() === "outbound-dial"
            ? true
            : false,
        body: conversation.body || "",
        type: conversation.type || "",
        notes: conversation.notes || "",
        isLiked: conversation.isLiked ?? null,
        tags: conversation.tags || [],
        clientEngagement: conversation.client_engagement || 0,
        most_talkative_speaker_name:
          conversation.most_talkative_speaker_name || "",
        total_speaking_time_seconds:
          conversation.total_speaking_time_seconds || 0,
        speaker_durations_list: conversation.speaker_durations_list || [],
      };
    });

    return { success: true, conversations, total: conversations.length };
  } catch (err: any) {
    console.error("Error in getting conversations by goal ID:", err.message);
    return { success: false, error: err.message };
  }
}

export async function GetEntrepriseConversationLogsStats() {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }

    const entrepriseID = entrepriseResponse.entreprise._id;
    await dbConnect();

    const stats = await Conversation.aggregate([
      { $match: { entreprise_id: entrepriseID } },
      {
        $facet: {
          clientsNotReached: [
            {
              $match: {
                type: "CALL",
                $or: [{ duration: "0" }, { voicemail_detected: true }],
              },
            },
            {
              $group: {
                _id: "$to_number",
                missedCall: { $first: { $eq: ["$duration", "0"] } },
                vmDetected: { $first: "$voicemail_detected" },
              },
            },
            {
              $group: {
                _id: null,
                total: { $sum: 1 },
                missedCalls: {
                  $sum: { $cond: ["$missedCall", 1, 0] },
                },
                vmDetected: {
                  $sum: { $cond: ["$vmDetected", 1, 0] },
                },
              },
            },
          ],

          answeredNotForwarded: [
            {
              $match: {
                duration: { $ne: "0" },
                $or: [
                  { child_type: { $exists: false } },
                  { child_type: null },
                  { child_type: "" },
                ],
              },
            },
            {
              $group: {
                _id: null,
                total: { $sum: 1 },
                notInterested: {
                  $sum: {
                    $cond: [
                      {
                        $regexMatch: {
                          input: "$summary",
                          regex: /pas intéressé/i,
                        },
                      },
                      1,
                      0,
                    ],
                  },
                },
                taNotAvailable: {
                  $sum: {
                    $cond: [
                      {
                        $regexMatch: {
                          input: "$summary",
                          regex: /TA.*(pas disponible|occupé)/i,
                        },
                      },
                      1,
                      0,
                    ],
                  },
                },
              },
            },
          ],

          forwardedToTA: [
            {
              $match: {
                duration: { $ne: "0" },
                child_type: "CALL",
              },
            },
            { $count: "total" },
          ],

          messageDueToMissed: [
            {
              $match: {
                duration: "0",
                child_type: { $in: ["SMS", "WHATSAPP"] },
              },
            },
            {
              $group: {
                _id: "$child_type",
                count: { $sum: 1 },
              },
            },
            {
              $group: {
                _id: null,
                total: { $sum: "$count" },
                whatsapp: {
                  $sum: {
                    $cond: [{ $eq: ["$_id", "WHATSAPP"] }, "$count", 0],
                  },
                },
                sms: {
                  $sum: {
                    $cond: [{ $eq: ["$_id", "SMS"] }, "$count", 0],
                  },
                },
              },
            },
          ],

          voicemailsSentDueToBusy: [
            {
              $match: {
                type: "VOICEMAIL",
                body: /busy/i,
              },
            },
            { $count: "total" },
          ],
        },
      },
    ]);

    const formatSingleCount = (arr: any[]) => arr[0]?.total || 0;
    const formatDetailed = (arr: any[]) => arr[0] || { total: 0 };

    const data = stats[0];
    console.log(data);
    return {
      success: true,
      stats: {
        clientsNotReached: formatDetailed(data.clientsNotReached),
        answeredNotForwarded: formatDetailed(data.answeredNotForwarded),
        transferredToTA: formatSingleCount(data.forwardedToTA),
        messageDueToMissed: formatDetailed(data.messageDueToMissed),
        voicemailsSent: formatSingleCount(data.voicemailsSentDueToBusy),
      },
    };
  } catch (err: any) {
    console.error(
      "Error in getting entreprise conversation logs stats:",
      err.message
    );
    return { success: false, error: err.message };
  }
}

export async function getCallLogsStatsForToday(timezone: string): Promise<{
  success: boolean;
  error?: string;
  stats?: {
    TotalClientsCalled: number;
    SuccessfulCalls: number;
    MissedCalls: number;
    AccountBalance: number;
  };
}> {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }
    const entrepriseID = entrepriseResponse.entreprise._id;

    await dbConnect();

    const goals = await Goal.find({ entreprise: entrepriseID })
      .populate("conversations")
      .lean();

    const conversations = goals.flatMap((goal) => goal.conversations);

    // Get start of today in the specified timezone
    const todayStart = DateTime.now()
      .setZone(timezone)
      .startOf("day")
      .toJSDate();

    const stats = conversations.reduce(
      (acc, conversation) => {
        if (!conversation) return acc;

        const conversationDate = new Date(conversation.initiated_time);
        if (conversationDate >= todayStart) {
          acc.TotalClientsCalled++;
          const callDuration = parseInt(conversation.duration, 10);
          if (isNaN(callDuration)) {
            console.log("Invalid CallDuration:", conversation.duration);
          } else if (callDuration > 0) {
            acc.SuccessfulCalls++;
          } else {
            acc.MissedCalls++;
          }
          acc.AccountBalance = 100;
        }

        return acc;
      },
      {
        TotalClientsCalled: 0,
        SuccessfulCalls: 0,
        MissedCalls: 0,
        AccountBalance: 0,
      }
    );

    return { success: true, stats };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function getEntrepriseTotalCosts() {
  try {
    const entrepriseResponse = await getEntrepriseByAdminID();
    if (!entrepriseResponse.success) {
      return { success: false, error: entrepriseResponse.error };
    }
    const entrepriseID = entrepriseResponse.entreprise._id;

    const result = await Conversation.aggregate([
      { $match: { entreprise_id: entrepriseID } },
      {
        $addFields: {
          durationSeconds: {
            $cond: [{ $eq: ["$type", "CALL"] }, { $toInt: "$duration" }, 0],
          },
          isMissedCall: {
            $cond: [
              {
                $and: [{ $eq: ["$type", "CALL"] }, { $eq: ["$duration", "0"] }],
              },
              1,
              0,
            ],
          },
          isAnsweredCall: {
            $cond: [
              {
                $and: [{ $eq: ["$type", "CALL"] }, { $ne: ["$duration", "0"] }],
              },
              1,
              0,
            ],
          },
          callCost: {
            $cond: [
              { $eq: ["$type", "CALL"] },
              {
                $multiply: [
                  { $ceil: { $divide: [{ $toInt: "$duration" }, 60] } },
                  0.22,
                ],
              },
              0,
            ],
          },
          smsCost: {
            $cond: [{ $eq: ["$type", "SMS"] }, 0.07, 0],
          },
          whatsappCost: {
            $cond: [{ $eq: ["$type", "WHATSAPP"] }, 0.1, 0],
          },
        },
      },
      {
        $group: {
          _id: null,
          totalCallCost: { $sum: "$callCost" },
          totalSMSCost: { $sum: "$smsCost" },
          totalWhatsappCost: { $sum: "$whatsappCost" },
          totalCost: {
            $sum: {
              $add: ["$callCost", "$smsCost", "$whatsappCost"],
            },
          },
          totalDurationSeconds: { $sum: "$durationSeconds" },
          missedCalls: { $sum: "$isMissedCall" },
          answeredCalls: { $sum: "$isAnsweredCall" },
        },
      },
    ]);
    console.log(result[0]);
    return { success: true, data: result[0] || {} };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
}

export async function UpdateConversationNotes(
  conversationId: string,
  notes: string
): Promise<{ success: boolean; error?: string }> {
  try {
    await dbConnect();
    const result = await Conversation.findByIdAndUpdate(
      conversationId,
      { $set: { notes } },
      { new: true }
    );
    if (!result) {
      return { success: false, error: "Conversation not found." };
    }
    return { success: true };
  } catch (e: any) {
    return { success: false, error: e.message };
  }
}

export async function getConversationNotesAndTagsById(conversationId: string) {
  try {
    await dbConnect();
    const conversation = await Conversation.findById(conversationId).lean();
    if (!conversation) {
      return { success: false, error: "Conversation not found." };
    }
    return {
      success: true,
      notes: conversation.notes || "",
      tags: conversation.tags || [],
    };
  } catch (e: any) {
    return { success: false, error: e.message };
  }
}

export async function addTagToConversation(
  conversationId: string,
  tagName: string,
  tagColor: string
): Promise<{ success: boolean; error?: string; tags?: any[] }> {
  try {
    await dbConnect();
    const result = await Conversation.findByIdAndUpdate(
      conversationId,
      { $push: { tags: { name: tagName, color: tagColor } } },
      { new: true }
    );
    if (!result) {
      return { success: false, error: "Conversation not found." };
    }
    return { success: true, tags: result.tags };
  } catch (e: any) {
    return { success: false, error: e.message };
  }
}

export async function removeTagFromConversation(
  conversationId: string,
  tagName: string,
  tagColor: string
): Promise<{ success: boolean; error?: string; tags?: any[] }> {
  try {
    await dbConnect();
    const result = await Conversation.findByIdAndUpdate(
      conversationId,
      { $pull: { tags: { name: tagName, color: tagColor } } },
      { new: true }
    );
    if (!result) {
      return { success: false, error: "Conversation not found." };
    }
    return { success: true, tags: result.tags };
  } catch (e: any) {
    return { success: false, error: e.message };
  }
}

export async function setConversationLikedStatus(
  conversationId: string,
  isLiked: boolean | null
): Promise<{ success: boolean; error?: string; isLiked?: boolean | null }> {
  try {
    await dbConnect();
    const result = await Conversation.findByIdAndUpdate(
      conversationId,
      { $set: { isLiked } },
      { new: true }
    );
    if (!result) {
      return { success: false, error: "Conversation not found." };
    }
    return { success: true, isLiked: result.isLiked };
  } catch (e: any) {
    return { success: false, error: e.message };
  }
}

export async function getConversationLiveTranscription(
  conversationId: string
): Promise<{
  success: boolean;
  error?: string;
  transcription?: any[];
}> {
  try {
    await dbConnect();

    const conversation = await Conversation.findById(conversationId)
      .select("deepgram_live_transcription")
      .lean();

    if (!conversation) {
      return { success: false, error: "Conversation not found." };
    }

    return {
      success: true,
      transcription: conversation.deepgram_live_transcription || [],
    };
  } catch (e: any) {
    console.error("Error getting live transcription:", e);
    return { success: false, error: e.message };
  }
}
